<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Timesheet Editor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="container mt-4">
    <h2 class="mb-4">Timesheet Editor</h2>
    
    <!-- File Upload -->
    <div class="mb-3">
        <input type="file" id="csvFileInput" class="form-control" accept=".csv">
    </div>
    <div class="border p-3 text-center" id="dropZone" style="border: 2px dashed #007bff; cursor: pointer;">
        Drag & Drop CSV Here
    </div>
    
    <!-- Coordinate Configuration -->
    <h4 class="mt-4">Configure Click Coordinates</h4>
    <div class="row">
        <div class="col-md-4">
            <label>Hours Field:</label>
            <input type="text" id="coordHours" class="form-control" value="598, 376">
        </div>
        <div class="col-md-4">
            <label>Task Field:</label>
            <input type="text" id="coordTask" class="form-control" value="705, 376">
        </div>
        <div class="col-md-4">
            <label>Add Task Button:</label>
            <input type="text" id="coordAdd" class="form-control" value="1280, 330">
        </div>
    </div>
    <!-- Tabs for Days -->
    <ul class="nav nav-tabs mt-4" id="dayTabs" role="tablist"></ul>
    <div class="tab-content mt-3" id="tabContent"></div>
    
    <!-- Download Button -->
    <button class="btn btn-primary mt-3" id="downloadCSV">Download CSV</button>
    <!-- Bootstrap Modal for File Processing Options -->
<div class="modal fade" id="csvOptionsModal" tabindex="-1" aria-labelledby="csvOptionsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="csvOptionsModalLabel">CSV Import Options</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>How would you like to process the CSV file?</p>
            </div>
            <div class="modal-footer">
                <button id="btnAppendCSV" class="btn btn-primary">Append Data</button>
                <button id="btnClearCSV" class="btn btn-danger">Clear & Load</button>
                <button id="btnCancelCSV" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

    <script>
        const daysOfWeek = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];

        document.addEventListener("DOMContentLoaded", initializeEmptyTimesheet);
        document.getElementById('csvFileInput').addEventListener('change', handleFileUpload);
        document.getElementById('dropZone').addEventListener('dragover', event => event.preventDefault());
        document.getElementById('dropZone').addEventListener('drop', handleFileDrop);
        document.getElementById('downloadCSV').addEventListener('click', downloadCSV);
        
        function initializeEmptyTimesheet(emptyTask=true) {
            const tabs = document.getElementById('dayTabs');
            const tabContent = document.getElementById('tabContent');
            tabs.innerHTML = '';
            tabContent.innerHTML = '';
            
            daysOfWeek.forEach((day, index) => {
                appendTab(day, index + 1,emptyTask);
            });
        }

        function appendTab(day, dayNumber,emptyTask=true) {
            const tabs = document.getElementById('dayTabs');
            const tabContent = document.getElementById('tabContent');
            
            const tab = document.createElement('li');
            tab.className = "nav-item";
            tab.innerHTML = `<button class="nav-link ${dayNumber === 1 ? 'active' : ''}" id="tab-${dayNumber}" data-bs-toggle="tab" data-bs-target="#content-${dayNumber}" type="button" role="tab">${day}</button>`;
            tabs.appendChild(tab);
            
            const content = document.createElement('div');
            content.className = `tab-pane fade ${dayNumber === 1 ? 'show active' : ''}`;
            content.id = `content-${dayNumber}`;
            content.innerHTML = `
                <div id="day-${dayNumber}" class="task-list"></div>
                <button class="btn btn-success mt-2" onclick="generateAHK(${dayNumber})">Generate ${day} Script</button>
            `;
            tabContent.appendChild(content);
            if(emptyTask){
                appendEmptyTask(dayNumber);
            }
        }

        function appendEmptyTask(day) {
            appendTask('', '', day);
        }

        function ensureEmptyTask(day) {
            const taskContainer = document.getElementById(`day-${day}`);
            const tasks = taskContainer.children;
            if (tasks.length === 0 || (tasks[tasks.length - 1].querySelector('.hour-input').value !== "" || tasks[tasks.length - 1].querySelector('.task-input').value !== "")) {
                appendEmptyTask(day);
            }
        }

        function showCopyDialog(button, currentDay) {
            const taskRow = button.parentElement;
            const hours = taskRow.children[0].value;
            const task = taskRow.children[1].value;
            let checkboxes = '';
            daysOfWeek.forEach((day, index) => {
                checkboxes += `<div><input type="checkbox" id="copyTo-${index + 1}" checked> ${day}</div>`;
            });
            
            const modal = document.createElement('div');
            modal.className = "modal fade show d-block";
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Copy Task</h5>
                            <button type="button" class="btn-close" onclick="closeModal(this)"></button>
                        </div>
                        <div class="modal-body">
                            ${checkboxes}
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary" onclick="copyTask(${currentDay}, '${hours}', '${task}', this)">Copy</button>
                            <button class="btn btn-secondary" onclick="closeModal(this)">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }
        
        function copyTask(currentDay, hours, task, button) {
            daysOfWeek.forEach((_, index) => {
                if (document.getElementById(`copyTo-${index + 1}`).checked && index + 1 !== currentDay) {
                    appendTask(hours, task, index + 1);
                }
            });
            closeModal(button);
        }
        
        function closeModal(button) {
            button.closest('.modal').remove();
        }

        function appendTask(hours, task, day) {
            const taskContainer = document.getElementById(`day-${day}`);
            const tasks = taskContainer.children;
            
            // Check if the last task is empty
            if (tasks.length > 0) {
                const lastTask = tasks[tasks.length - 1];
                const lastHourInput = lastTask.querySelector('.hour-input');
                const lastTaskInput = lastTask.querySelector('.task-input');
                
                if (lastHourInput.value.trim() === "" && lastTaskInput.value.trim() === "") {
                    lastTask.remove(); // Remove last empty task before adding a new one
                }
            }
            
            // Create new task row
            const taskRow = document.createElement('div');
            taskRow.className = "d-flex gap-2 mb-2 align-items-center";
            taskRow.innerHTML = `
                <input type="number" class="form-control w-25 hour-input" min="0" oninput="this.value = Math.round(this.value);" placeholder="Hours" value="${hours}" data-day="${day}">
                <input type="text" class="form-control task-input" placeholder="Task" value="${task}">
                <button class="btn btn-danger" onclick="deleteTask(this, ${day})">🗑️</button>
                <button class="btn btn-info" onclick="showCopyDialog(this, ${day})">📋</button>
            `;
            
            taskContainer.appendChild(taskRow);
            
            // Ensure there is always an empty task at the end
            taskRow.querySelector('.hour-input').addEventListener('input', function () {
                ensureEmptyTask(day);
            });
            taskRow.querySelector('.task-input').addEventListener('input', function () {
                ensureEmptyTask(day);
            });
        
            ensureEmptyTask(day);
        }
        

        function deleteTask(button, day) {
            button.parentElement.remove();
            ensureEmptyTask(day);
        }

        let fileToProcess = null; // Store the file temporarily
        let csvModalInstance = null; // Store Bootstrap modal instance
        
        document.getElementById('csvFileInput').addEventListener('change', handleFileUpload);
        document.getElementById('dropZone').addEventListener('dragover', event => event.preventDefault());
        document.getElementById('dropZone').addEventListener('drop', handleFileDrop);
        
        function handleFileUpload(event) {
            fileToProcess = event.target.files[0]; // Store selected file
            if (fileToProcess) {
                showCSVOptionsModal();
            }
        }
        
        function handleFileDrop(event) {
            event.preventDefault();
            fileToProcess = event.dataTransfer.files[0]; // Store dropped file
            if (fileToProcess) {
                showCSVOptionsModal();
            }
        }
        
        function showCSVOptionsModal() {
            // Initialize Bootstrap modal if not already initialized
            if (!csvModalInstance) {
                csvModalInstance = new bootstrap.Modal(document.getElementById('csvOptionsModal'));
            }
            csvModalInstance.show();
        }
        
        // Attach event listeners for modal buttons
        document.getElementById('btnAppendCSV').addEventListener('click', () => handleCSVAction('append'));
        document.getElementById('btnClearCSV').addEventListener('click', () => handleCSVAction('clear'));
        document.getElementById('btnCancelCSV').addEventListener('click', () => {
            fileToProcess = null; // Reset file
            alert("CSV load canceled.");
        });
        
        // Core function for handling CSV actions
        
function handleCSVAction(action) {
    if (!fileToProcess) return;

    if (csvModalInstance) {
        csvModalInstance.hide();
    }

    const reader = new FileReader();
    reader.onload = function (e) {
        const lines = e.target.result.split('\n').slice(1); 

        if (action === "clear") {
            initializeEmptyTimesheet(false); 
        }

        let lastDay = null;
        lines.forEach(line => {
            const [hours, task, day] = line.split(',');
            if (hours.trim() && task.trim() && day.trim()) {
                lastDay = parseInt(day.trim(), 10);
                appendTask(hours.trim(), task.trim(), lastDay);
            }
        });

        fileToProcess = null;
    };
    reader.readAsText(fileToProcess);
}
        


        function downloadCSV() {
            let csv = 'Hours,Task,Day\n';
            daysOfWeek.forEach((day, index) => {
                document.querySelectorAll(`#day-${index + 1} .d-flex`).forEach(taskRow => {
                    const inputs = taskRow.querySelectorAll('input');
                    if (inputs[0].value && inputs[1].value) {
                        csv += `${inputs[0].value},${inputs[1].value},${index + 1}\n`;
                    }
                });
            });
            downloadFile(csv, 'timesheet.csv', 'text/csv');
        }

        function generateAHK(day) {
            const coordHours = document.getElementById('coordHours').value;
            const coordTask = document.getElementById('coordTask').value;
            const coordAdd = document.getElementById('coordAdd').value;
            
            let script = `#Requires AutoHotkey v2.0\n\nCoordMode "Mouse", "Screen"\n\n`;
            document.querySelectorAll(`#day-${day} .d-flex`).forEach(taskRow => {
                const inputs = taskRow.querySelectorAll('input');
                if (inputs[0].value && inputs[1].value) {
                    script += `Click ${coordHours}\nSend "${inputs[0].value}"\nClick ${coordTask}\nSend "${inputs[1].value}"\nClick ${coordAdd}\nSleep 1000\n`;
                }
            });
            downloadFile(script, `FlowHR_${daysOfWeek[day - 1]}.ahk`, 'text/plain');
        }

        function downloadFile(content, filename, type) {
            const blob = new Blob([content], { type });
            const a = document.createElement('a');
            a.href = URL.createObjectURL(blob);
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
