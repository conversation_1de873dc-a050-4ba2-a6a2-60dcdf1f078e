import streamlit as st
import pandas as pd
from PIL import Image
import base64
import io
import os
from dotenv import load_dotenv
from langchain_openai import AzureChatOpenAI
from langchain.schema import HumanMessage
import json
from datetime import datetime, timedelta
import re
import config

# Load environment variables
load_dotenv()

# Configure page
st.set_page_config(**config.PAGE_CONFIG)

# Initialize Azure OpenAI
@st.cache_resource
def init_azure_openai():
    # Validate environment variables
    api_key = os.getenv("API_KEY")
    api_version = os.getenv("API_VERSION")
    azure_endpoint = os.getenv("AZURE_ENDPOINT")
    model = os.getenv("MODEL")

    if not api_key:
        raise ValueError("API_KEY environment variable is required")
    if not api_version:
        raise ValueError("API_VERSION environment variable is required")
    if not azure_endpoint:
        raise ValueError("AZURE_ENDPOINT environment variable is required")
    if not model:
        raise ValueError("MODEL environment variable is required")

    return AzureChatOpenAI(
        azure_endpoint=azure_endpoint,
        api_key=api_key,
        api_version=api_version,
        azure_deployment=model,
        temperature=0.1
    )

def encode_image_to_base64(image):
    """Convert PIL image to base64 string"""
    buffered = io.BytesIO()
    image.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode()
    return img_str

def extract_meetings_from_image(llm, image):
    """Extract meeting information from calendar screenshot using Azure OpenAI Vision"""

    # Convert image to base64
    img_base64 = encode_image_to_base64(image)

    prompt = config.MEETING_EXTRACTION_PROMPT
    
    try:
        # Create message with image
        message = HumanMessage(
            content=[
                {"type": "text", "text": prompt},
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/png;base64,{img_base64}"}
                }
            ]
        )
        
        response = llm.invoke([message])
        
        # Extract JSON from response
        response_text = response.content
        
        # Try to find JSON in the response
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group()
            return json.loads(json_str)
        else:
            st.error("Could not extract JSON from AI response")
            return {"meetings": []}
            
    except Exception as e:
        st.error(f"Error processing image: {str(e)}")
        return {"meetings": []}

def meetings_to_timesheet_data(meetings_data):
    """Convert meetings data to timesheet format"""
    timesheet_data = []

    for meeting in meetings_data.get("meetings", []):
        # Apply duration rules
        duration = meeting.get("duration", config.DURATION_RULES["min_duration"])
        duration = max(duration, config.DURATION_RULES["min_duration"])
        duration = min(duration, config.DURATION_RULES["max_auto_duration"])

        # Round to nearest increment
        increment = config.DURATION_RULES["rounding_increment"]
        duration = round(duration / increment) * increment

        timesheet_data.append({
            "Hours": duration,
            "Task": meeting.get("title", "Meeting"),
            "Day": config.DAY_MAPPING.get(meeting.get("day", "Unknown"), 1)
        })

    return timesheet_data

def generate_ahk_script(timesheet_data, coord_hours=None, coord_task=None, coord_add=None):
    """Generate AutoHotkey script from timesheet data"""

    # Use default coordinates if not provided
    coord_hours = coord_hours or config.DEFAULT_COORDINATES["hours_field"]
    coord_task = coord_task or config.DEFAULT_COORDINATES["task_field"]
    coord_add = coord_add or config.DEFAULT_COORDINATES["add_button"]

    # Build script body
    script_body = ""
    for entry in timesheet_data:
        hours = entry["Hours"]
        task = entry["Task"].replace('"', '""')  # Escape quotes for AHK
        script_body += f'    Click {coord_hours}\n    Send "{hours}"\n    Click {coord_task}\n    Send "{task}"\n    Click {coord_add}\n    Sleep 1000\n\n'

    # Use template from config
    script = config.AHK_SCRIPT_TEMPLATE.format(
        hours_coord=coord_hours,
        task_coord=coord_task,
        add_coord=coord_add,
        script_body=script_body
    )

    return script

def main():
    st.title("📅 Teams Calendar to Timesheet Automation")
    st.markdown("Upload a screenshot of your Microsoft Teams calendar and automatically generate timesheet data and AutoHotkey scripts!")
    
    # Initialize Azure OpenAI
    try:
        llm = init_azure_openai()
        st.success(config.SUCCESS_MESSAGES["connection_established"])
    except Exception as e:
        st.error(f"{config.ERROR_MESSAGES['connection_failed']}: {str(e)}")
        st.stop()

    # Store llm in session state for use throughout the app
    st.session_state.llm = llm
    
    # Sidebar for configuration
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        st.subheader("AutoHotkey Coordinates")
        coord_hours = st.text_input("Hours Field Coordinates", value=config.DEFAULT_COORDINATES["hours_field"])
        coord_task = st.text_input("Task Field Coordinates", value=config.DEFAULT_COORDINATES["task_field"])
        coord_add = st.text_input("Add Task Button Coordinates", value=config.DEFAULT_COORDINATES["add_button"])

        st.info(config.UI_TEXT["coordinates_help"])
    
    # Main content area
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.header("📤 Upload Calendar Screenshot")
        
        uploaded_file = st.file_uploader(
            "Choose a Teams calendar screenshot",
            type=config.SUPPORTED_IMAGE_FORMATS,
            help=config.UI_TEXT["upload_help"]
        )
        
        if uploaded_file is not None:
            # Display uploaded image
            image = Image.open(uploaded_file)
            st.image(image, caption="Uploaded Calendar Screenshot", use_column_width=True)
            
            # Process button
            if st.button("🔍 Extract Meetings", type="primary"):
                with st.spinner("Analyzing calendar screenshot..."):
                    # Extract meetings using AI
                    meetings_data = extract_meetings_from_image(st.session_state.llm, image)

                    if meetings_data and meetings_data.get("meetings"):
                        st.session_state.meetings_data = meetings_data
                        st.session_state.timesheet_data = meetings_to_timesheet_data(meetings_data)
                        st.success(config.SUCCESS_MESSAGES["meetings_extracted"].format(count=len(meetings_data['meetings'])))
                    else:
                        st.warning(config.ERROR_MESSAGES["no_meetings_found"])
    
    with col2:
        st.header("📊 Extracted Data")
        
        if hasattr(st.session_state, 'meetings_data') and st.session_state.meetings_data:
            # Display extracted meetings
            st.subheader("🎯 Detected Meetings")
            
            meetings_df = pd.DataFrame(st.session_state.meetings_data["meetings"])
            st.dataframe(meetings_df, use_container_width=True)
            
            # Display timesheet data
            st.subheader("📋 Timesheet Data")
            timesheet_df = pd.DataFrame(st.session_state.timesheet_data)
            
            # Allow editing of timesheet data
            edited_df = st.data_editor(
                timesheet_df,
                use_container_width=True,
                num_rows="dynamic"
            )
            
            # Update session state with edited data
            st.session_state.timesheet_data = edited_df.to_dict('records')
            
            # Download buttons
            st.subheader("📥 Download Options")
            
            col_csv, col_ahk = st.columns(2)
            
            with col_csv:
                # CSV download
                csv_data = edited_df.to_csv(index=False)
                st.download_button(
                    label="📄 Download CSV",
                    data=csv_data,
                    file_name=f"timesheet_{datetime.now().strftime('%Y%m%d')}.csv",
                    mime="text/csv"
                )
            
            with col_ahk:
                # AutoHotkey script download
                ahk_script = generate_ahk_script(
                    st.session_state.timesheet_data,
                    coord_hours, coord_task, coord_add
                )
                st.download_button(
                    label="🤖 Download AHK Script",
                    data=ahk_script,
                    file_name=f"FlowHR_timesheet_{datetime.now().strftime('%Y%m%d')}.ahk",
                    mime="text/plain"
                )
            
            # Preview AHK script
            with st.expander("👀 Preview AutoHotkey Script"):
                st.code(ahk_script, language="autohotkey")
        
        else:
            st.info("👆 Upload a calendar screenshot to get started!")
    
    # Instructions
    with st.expander("📖 How to Use"):
        st.markdown("""
        ### Step-by-step Instructions:
        
        1. **Take a Screenshot**: Capture your Microsoft Teams calendar view showing your meetings
        2. **Upload Image**: Use the file uploader to select your screenshot
        3. **Extract Data**: Click "Extract Meetings" to analyze the image with AI
        4. **Review & Edit**: Check the extracted data and make any necessary corrections
        5. **Configure Coordinates**: Set the screen coordinates for your FlowHR interface
        6. **Download**: Get your CSV file and AutoHotkey script
        7. **Run Script**: Execute the .ahk file to automatically fill your timesheet
        
        ### Tips for Best Results:
        - Use a clear, high-resolution screenshot
        - Ensure meeting times and titles are clearly visible
        - Include the day labels if possible
        - Make sure there's good contrast in the image
        """)

if __name__ == "__main__":
    main()
