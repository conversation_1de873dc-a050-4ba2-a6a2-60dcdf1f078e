# Teams Calendar to Timesheet Automation

Una aplicación de Streamlit que utiliza IA (Azure OpenAI) para extraer automáticamente reuniones de screenshots de Microsoft Teams y generar scripts de automatización para llenar timesheets.

## 🚀 Características

- **Análisis de Imágenes con IA**: Utiliza Azure OpenAI Vision para extraer información de reuniones desde screenshots
- **Extracción Automática**: Detecta títulos, horarios y duración de reuniones
- **Edición Interactiva**: Permite revisar y editar los datos extraídos
- **Generación de Scripts**: Crea scripts AutoHotkey para automatizar el llenado de timesheets
- **Exportación CSV**: Genera archivos CSV compatibles con el sistema existente

## 📋 Requisitos

- Python 3.8+
- Cuenta de Azure OpenAI con acceso a modelos de visión
- AutoHotkey v2.0 (para ejecutar los scripts generados)

## 🛠️ Instalación

### Opción 1: Instalación Automática (Recomendada)

**Windows:**
```bash
install.bat
```

**Linux/Mac:**
```bash
chmod +x install.sh
./install.sh
```

### Opción 2: Instalación Manual

1. **Clonar o descargar el proyecto**
```bash
cd FlowHr
```

2. **Instalar dependencias**
```bash
pip install -r requirements.txt
```

3. **Configurar variables de entorno**
Copia el archivo de ejemplo y edítalo con tus credenciales:
```bash
cp .env.example .env
```

Edita el archivo `.env` con tus credenciales de Azure OpenAI:
```env
API_KEY=tu_api_key_aqui
API_VERSION=2024-02-15-preview
AZURE_ENDPOINT=https://tu-recurso.openai.azure.com/
MODEL=nombre-de-tu-deployment
```

**Importante**: `MODEL` debe ser el nombre de tu deployment en Azure, no el nombre del modelo.

## 🚀 Uso

1. **Ejecutar la aplicación**
```bash
streamlit run streamlit_app.py
```

2. **Abrir en el navegador**
La aplicación se abrirá automáticamente en `http://localhost:8501`

3. **Subir screenshot**
- Toma un screenshot claro de tu calendario de Microsoft Teams
- Sube la imagen usando el botón de carga

4. **Extraer datos**
- Haz clic en "Extract Meetings" para analizar la imagen
- Revisa y edita los datos extraídos si es necesario

5. **Configurar coordenadas**
- Ajusta las coordenadas de pantalla para tu interfaz de FlowHR
- Usa herramientas como "Mouse Position" para encontrar las coordenadas exactas

6. **Descargar archivos**
- Descarga el archivo CSV para importar manualmente
- Descarga el script AutoHotkey para automatización completa

## 📸 Consejos para Screenshots

Para obtener mejores resultados:
- Usa screenshots de alta resolución
- Asegúrate de que los títulos y horarios sean claramente visibles
- Incluye las etiquetas de días si es posible
- Evita superposiciones o ventanas que oculten información

## 🤖 AutoHotkey

Los scripts generados son compatibles con AutoHotkey v2.0 y automatizan:
- Hacer clic en campos de horas
- Introducir la duración
- Hacer clic en campos de tarea
- Introducir el nombre de la reunión
- Hacer clic en botón "Agregar"

## 🔧 Configuración Avanzada

### Coordenadas de Pantalla
Ajusta estas coordenadas según tu configuración de FlowHR:
- **Hours Field**: Campo donde se introducen las horas
- **Task Field**: Campo donde se introduce el nombre de la tarea
- **Add Task Button**: Botón para agregar la tarea

### Modelo de IA
Puedes cambiar el modelo en el archivo `.env`:
- `gpt-4-vision-preview`: Mejor precisión
- `gpt-4o`: Más rápido y económico

## 📁 Estructura del Proyecto

```
FlowHr/
├── streamlit_app.py          # Aplicación principal de Streamlit
├── requirements.txt          # Dependencias de Python
├── .env                     # Variables de entorno (configurar)
├── README.md               # Este archivo
├── Timesheet-Editor 3.html # Editor web original (referencia)
└── timesheet.csv          # Datos de ejemplo
```

## 🐛 Solución de Problemas

### Error: "Client.init() got an unexpected keyword argument 'proxies'"
Este error indica versiones incompatibles de las librerías. Solución:
```bash
pip uninstall langchain langchain-openai openai
pip install -r requirements.txt
```

### Error de conexión a Azure OpenAI
- Verifica que las credenciales en `.env` sean correctas
- Asegúrate de que tu recurso de Azure OpenAI esté activo
- Confirma que tienes acceso al modelo especificado
- **Importante**: Usa el nombre del deployment, no el nombre del modelo

### Extracción imprecisa
- Usa screenshots más claros y de mayor resolución
- Asegúrate de que el texto sea legible
- Evita capturas con superposiciones o elementos que oculten información

### Scripts AutoHotkey no funcionan
- Verifica que las coordenadas sean correctas para tu pantalla
- Asegúrate de tener AutoHotkey v2.0 instalado
- Ejecuta el script como administrador si es necesario

## 📞 Soporte

Para problemas o sugerencias, revisa:
1. Los logs de la aplicación Streamlit
2. La consola del navegador para errores JavaScript
3. Los archivos de log de AutoHotkey

## 🔄 Migración desde el Editor HTML

Esta aplicación reemplaza el flujo manual del editor HTML:
1. **Antes**: Screenshot → Entrada manual → Generación de script
2. **Ahora**: Screenshot → IA extrae datos → Revisión → Script automático

Los archivos CSV generados son compatibles con el sistema anterior.
