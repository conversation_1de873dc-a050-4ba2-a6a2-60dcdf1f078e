#!/bin/bash

echo "🚀 Installing Teams Calendar to Timesheet Automation"
echo "================================================"

echo "📦 Upgrading pip..."
python3 -m pip install --upgrade pip

echo "📦 Installing dependencies..."
pip3 install -r requirements.txt

echo "⚙️ Setting up environment..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created .env file from template"
    echo "⚠️  Please edit .env file with your Azure OpenAI credentials"
else
    echo "✅ .env file already exists"
fi

echo "🧪 Running tests..."
python3 test_app.py

echo ""
echo "🎉 Installation complete!"
echo ""
echo "📋 Next steps:"
echo "1. Edit .env file with your Azure OpenAI credentials"
echo "2. Run: streamlit run streamlit_app.py"
echo "3. Open your browser to http://localhost:8501"
echo ""
