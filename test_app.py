#!/usr/bin/env python3
"""
Test script for the Teams Calendar to Timesheet application
"""

import os
import sys
from dotenv import load_dotenv

def check_environment():
    """Check if all required environment variables are set"""
    load_dotenv()
    
    required_vars = ["API_KEY", "API_VERSION", "AZURE_ENDPOINT", "MODEL"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease check your .env file and ensure all variables are set.")
        return False
    
    print("✅ All environment variables are set correctly")
    return True

def check_dependencies():
    """Check if all required packages are installed"""
    required_packages = [
        "streamlit",
        "langchain",
        "langchain_openai", 
        "python-dotenv",
        "Pillow",
        "pandas",
        "opencv-python",
        "openai"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nPlease install missing packages with:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ All required packages are installed")
    return True

def test_azure_connection():
    """Test connection to Azure OpenAI"""
    try:
        from langchain_openai import AzureChatOpenAI
        from langchain.schema import HumanMessage

        # Validate environment variables first
        api_key = os.getenv("API_KEY")
        api_version = os.getenv("API_VERSION")
        azure_endpoint = os.getenv("AZURE_ENDPOINT")
        model = os.getenv("MODEL")

        if not all([api_key, api_version, azure_endpoint, model]):
            print("❌ Missing required environment variables")
            return False

        llm = AzureChatOpenAI(
            azure_endpoint=azure_endpoint,
            api_key=api_key,
            api_version=api_version,
            azure_deployment=model,
            temperature=0.1
        )

        # Test with a simple message
        message = HumanMessage(content="Hello, this is a test.")
        response = llm.invoke([message])
        if response:
            print("✅ Azure OpenAI connection successful")
            return True
        else:
            print("❌ No response from Azure OpenAI")
            return False

    except Exception as e:
        print(f"❌ Azure OpenAI connection failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Teams Calendar to Timesheet Application")
    print("=" * 50)
    
    tests = [
        ("Environment Variables", check_environment),
        ("Dependencies", check_dependencies),
        ("Azure OpenAI Connection", test_azure_connection)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}...")
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! You can now run the application with:")
        print("streamlit run streamlit_app.py")
    else:
        print("❌ Some tests failed. Please fix the issues above before running the application.")
        sys.exit(1)

if __name__ == "__main__":
    main()
