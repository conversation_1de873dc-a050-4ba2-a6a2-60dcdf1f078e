# Configuration file for the Teams Calendar to Timesheet application

# Default coordinates for FlowHR interface
DEFAULT_COORDINATES = {
    "hours_field": "598, 376",
    "task_field": "705, 376", 
    "add_button": "1280, 330"
}

# Day mapping for timesheet
DAY_MAPPING = {
    "Monday": 1,
    "Tuesday": 2, 
    "Wednesday": 3,
    "Thursday": 4,
    "Friday": 5,
    "Saturday": 6,
    "Sunday": 7,
    "Unknown": 1
}

# Meeting duration rounding rules
DURATION_RULES = {
    "min_duration": 0.5,  # Minimum duration in hours
    "rounding_increment": 0.5,  # Round to nearest 0.5 hours
    "max_auto_duration": 8.0  # Maximum reasonable meeting duration
}

# AI prompt template for meeting extraction
MEETING_EXTRACTION_PROMPT = """
Analyze this Microsoft Teams calendar screenshot and extract all meeting information.

For each meeting, identify:
1. Meeting title/name (clean, professional format)
2. Start time (in HH:MM 24-hour format)
3. End time (in HH:MM 24-hour format) 
4. Duration in hours (calculate from start/end time, round to nearest 0.5 hours)
5. Day of the week if visible (Monday, Tuesday, etc.)

Return the information in JSON format like this:
{
    "meetings": [
        {
            "title": "Meeting Name",
            "start_time": "09:00",
            "end_time": "10:00", 
            "duration": 1.0,
            "day": "Monday"
        }
    ]
}

Rules:
- If duration is less than 30 minutes, round to 0.5 hours
- If duration is between 30-90 minutes, round to 1 hour  
- For longer meetings, round to nearest 0.5 hours
- Only extract actual meetings, not calendar events like "Lunch", "Break", "Personal Time"
- Clean up meeting titles (remove redundant text, focus on core purpose)
- If day is not clearly visible, use "Unknown"
- Ignore cancelled or tentative meetings
- Focus on work-related meetings only
"""

# AutoHotkey script template
AHK_SCRIPT_TEMPLATE = """#Requires AutoHotkey v2.0

; FlowHR Timesheet Automation Script
; Generated automatically from Teams calendar

CoordMode "Mouse", "Screen"

; Configuration
HoursCoord := "{hours_coord}"
TaskCoord := "{task_coord}" 
AddCoord := "{add_coord}"
DelayTime := 1000  ; Milliseconds between actions

; Main automation function
AutoFillTimesheet() {{
{script_body}
}}

; Execute the automation
AutoFillTimesheet()

; Optional: Show completion message
MsgBox "Timesheet automation completed!", "FlowHR Automation", 0
"""

# Supported image formats
SUPPORTED_IMAGE_FORMATS = ['png', 'jpg', 'jpeg', 'bmp', 'tiff']

# Streamlit page configuration
PAGE_CONFIG = {
    "page_title": "Teams Calendar to Timesheet Automation",
    "page_icon": "📅",
    "layout": "wide"
}

# Error messages
ERROR_MESSAGES = {
    "no_api_key": "❌ Azure OpenAI API key not found. Please check your .env file.",
    "connection_failed": "❌ Failed to connect to Azure OpenAI. Please verify your credentials.",
    "no_meetings_found": "⚠️ No meetings found in the image. Please try a clearer screenshot.",
    "invalid_image": "❌ Invalid image format. Please upload a PNG, JPG, or JPEG file.",
    "processing_error": "❌ Error processing image. Please try again with a different screenshot."
}

# Success messages  
SUCCESS_MESSAGES = {
    "connection_established": "✅ Azure OpenAI connection established",
    "meetings_extracted": "✅ Successfully extracted {count} meetings!",
    "script_generated": "✅ AutoHotkey script generated successfully",
    "csv_exported": "✅ CSV file exported successfully"
}

# UI text and labels
UI_TEXT = {
    "upload_help": "Upload a clear screenshot of your Microsoft Teams calendar showing the meetings",
    "coordinates_help": "💡 Tip: Use a screen coordinate tool to find the exact pixel coordinates for your FlowHR interface",
    "preview_help": "Review the extracted data and make any necessary corrections before downloading"
}
