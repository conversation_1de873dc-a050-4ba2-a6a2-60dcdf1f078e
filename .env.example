# Azure OpenAI Configuration
# Copy this file to .env and fill in your actual values

# Your Azure OpenAI API Key
API_KEY=your_api_key_here

# API Version (use 2024-02-15-preview for vision models)
API_VERSION=2024-02-15-preview

# Your Azure OpenAI Endpoint URL
AZURE_ENDPOINT=https://your-resource-name.openai.azure.com/

# Model deployment name (this is the name you gave to your deployment in Azure, not the model name)
# Example: if you deployed gpt-4-vision-preview and named it "gpt-4-vision", use "gpt-4-vision"
MODEL=your-deployment-name

# Optional: Additional configuration
# TEMPERATURE=0.1
# MAX_TOKENS=1000

# Note: Make sure your Azure OpenAI deployment supports vision capabilities
# Supported models: gpt-4-vision-preview, gpt-4o, gpt-4-turbo (with vision)
